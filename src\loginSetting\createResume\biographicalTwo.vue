<route lang="json5">
{
  layout: 'default',
  style: {
    navigationBarTitleText: '',
    enablePullDownRefresh: false,
    navigationStyle: 'custom',
  },
}
</route>

<template>
  <view class="bg-img">
    <CustomNavBar>
      <template #right>
        <wd-img :src="identitySwitchingImg" width="45rpx" height="45rpx" @click="changeIdentFun" />
      </template>
    </CustomNavBar>
    <view class="page_box">
      <view class="page_padding">
        <view class="page_flex_row">
          <view class="page_flex">
            <view class="page_flex_left">身份创建</view>
            <view class="page_flex_icon" :style="{ top: statusBarHeight * 2 + 'rpx' }">
              <!-- <image src="../img/ml.png" mode="aspectFill"></image> -->
              <view class="page_flex_img"></view>
            </view>
          </view>
          <view class="page_input text-wrap">
            在线简历将会向企业展示，我们会妥善保护你的隐私， 后续你也可以在设置中将简历隐藏～
          </view>
        </view>
        <view class="page_flex_row_bottom">
          <view class="tag-select-r-list">
            <view
              @click="changeactive(index, item)"
              class="tag-select-r"
              :class="index === activeIndex ? 'myStyle-box' : ''"
              v-for="(item, index) in shList"
              :key="index"
            >
              {{ item.name }}
            </view>
          </view>
        </view>
        <view class="page_flex_row_bottom">
          <view class="tag-name">求职状态</view>
          <view class="tag-select-r-list">
            <view
              @click="changeactiveTwo(index, item)"
              class="tag-select-r text-28rpx p-tb-20"
              :style="index === 2 ? 'width:560rpx' : ''"
              :class="index === activeIndexTwo ? 'myStyle-box text-28rpx' : ''"
              v-for="(item, index) in qzList"
              :key="index"
            >
              {{ item.name }}
            </view>
          </view>
        </view>
      </view>
    </view>

    <view class="btn_fixed" @click="submit">
      <view class="btn_box">
        <view class="btn_bg">下一步</view>
      </view>
    </view>
  </view>
</template>

<script setup>
import CustomNavBar from '@/components/CustomNavBar/CustomNavBar.vue'
import { getStatusBar, getCheackInfo, getInfo } from '@/utils/storage'
import { userResumeBaseInfo, updateBaseInfo, resumeBaseInfoList } from '@/interPost/biographical'
import identitySwitchingImg from '@/static/mine/business/identity-switching.png'
const { changeIdent } = useChangeIdent()
const isAdd = ref(true)
const formData = reactive({
  trueName: '',
  sex: null,
  birthday: '',
  identityType: 1,
  seekStatus: 0,
  id: null,
})
const show = ref(true)
const statusBarHeight = ref(0)
const activeIndex = ref(0)
const activeIndexTwo = ref(0)
const value = ref(0)
const shList = reactive([
  {
    name: '职场人',
    value: 1,
  },
  {
    name: '学生',
    value: 2,
  },
])
const qzList = reactive([
  {
    name: '离职-随时到岗',
    value: 0,
  },
  {
    name: '在职-考虑机会',
    value: 1,
  },
  {
    name: '观望机会',
    value: 2,
  },
])
// 切换身份
const changeIdentFun = async () => {
  changeIdent()
}
onLoad((options) => {
  console.log(options, 'options==')
  formData.trueName = options.trueName
  formData.id = getCheackInfo().baseInfoId ? getCheackInfo().baseInfoId : null
  formData.sex = options.sex
  formData.birthday = options.birthday
  statusBarHeight.value = getStatusBar()
})
onShow(() => {
  getInfoList()
})
const getInfoList = async () => {
  await uni.$onLaunched
  const res = await resumeBaseInfoList({
    id: getCheackInfo().baseInfoId,
  })
  console.log(res, '==')
  if (res.code === 0) {
    if (Object.prototype.hasOwnProperty.call(res, 'data')) {
      console.log(res, 'res====查到的')
      isAdd.value = false
      activeIndex.value = res.data.identityType === 1 ? 0 : 1
      activeIndexTwo.value = res.data.seekStatus === 0 ? 0 : res.data.seekStatus === 1 ? 1 : 2
      formData.identityType = res.data.identityType
      formData.seekStatus = res.data.seekStatus
    } else {
      isAdd.value = true
    }
  } else {
    uni.showToast({
      title: res.msg,
      icon: 'none',
      duration: 3000,
    })
  }
}
const changeactive = (index, item) => {
  activeIndex.value = index
  formData.identityType = item.value
}
const changeactiveTwo = (index, item) => {
  activeIndexTwo.value = index
  formData.seekStatus = item.value
}
const submit = async () => {
  let res
  if (isAdd.value) {
    res = await userResumeBaseInfo(formData)
  } else {
    res = await updateBaseInfo(formData)
  }
  console.log(res, 'res=====')
  if (res.code === 0) {
    uni.navigateTo({
      url: '/loginSetting/category/JobIntention',
    })
  } else {
    uni.showToast({
      title: res.msg,
      icon: 'none',
      duration: 3000,
    })
  }
}
</script>

<style lang="scss" scoped>
::v-deep .u-input__content__field-wrapper__field {
  height: 70rpx !important;
}

.page_flex_left::after {
  position: absolute;
  bottom: 15rpx;
  left: 0rpx;
  width: 200rpx;
  height: 8rpx;
  content: '';
  background: linear-gradient(90deg, #ffc2c2 0%, #dddcff 100%);
  border-radius: 32rpx;
}

.btn_fixed {
  position: fixed;
  bottom: 80rpx;
  width: 100%;

  .btn_box {
    box-sizing: border-box;
    width: 100%;
    padding: 50rpx;
    padding-bottom: 0;

    .btn_bg {
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 30rpx;
      font-size: 16px;
      font-weight: 500;
      color: #333333;
      background: linear-gradient(90deg, #ffc2c2 0%, #dddcff 100%);
      border-radius: 14px 14px 14px 14px;
    }
  }
}

.page_flex_for {
  display: flex;
  flex-direction: row;

  .page_flex_list {
    display: flex;
    flex-direction: row;
    margin-left: 20rpx;

    .page_select_border {
      padding: 0 22rpx;
      font-size: 22rpx;
      line-height: 44rpx;
      color: #3e9cff;
      background: #f1f1ff;
      border: 1px solid #3e9cff;
      border-radius: 5px 5px 5px 5px;
    }

    .page_border {
      padding: 0 22rpx;
      font-size: 22rpx;

      line-height: 44rpx;
      color: #777777;
      background: #f5f5f5;
      border-radius: 5px 5px 5px 5px;
    }
  }
}

.input_font {
  font-size: 22rpx;
  font-weight: 400;
  line-height: 44rpx;
  color: #777777;
}

.page_box {
  box-sizing: border-box;
  width: 100%;
  padding: 50rpx;
  padding-top: 140rpx;
}

.page_flex_left_just {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  width: 100%;
}

.page_flex_row_bottom {
  width: 100%;
  // padding-bottom: 30rpx;
  padding-top: 30rpx;
}

.tag-name {
  font-size: 32rpx;
  font-weight: 500;
  color: #000;
}

.tag-select-r-list {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  justify-content: space-between;
}

.tag-select-r {
  width: 270rpx;
  padding: 15rpx 0rpx;
  margin: 20rpx 0rpx;
  font-size: 32rpx;
  font-weight: 500;
  color: #000;
  text-align: center;
  background-color: #f2f2f2;
  border: 1rpx solid transparent;
  border-radius: 10rpx;
}

.p-tb-20 {
  padding: 20rpx 0rpx;
}

.myStyle-box {
  font-weight: 500;
  color: #3e9cff;
  background-color: #f1f1ff;
  border: 1rpx solid #3e9cff;
}

.text-28rpx {
  font-size: 28rpx;
}

.page_padding {
  height: 1000rpx;
  padding: 50rpx 50rpx;
  background: #ffffff;
  border-radius: 20px 20px 20px 20px;
  box-shadow: 0px 4px 14px 0px rgba(0, 0, 0, 0.1);
}

.page_input {
  padding-top: 16rpx;
  font-size: 26rpx;
  color: #666;
}

.page_input1 {
  padding-top: 10rpx;
}

.page_flex_left_row {
  display: flex;
  flex-direction: row;
  align-items: center;

  .page_flex_left_color {
    font-size: 11px;
    font-weight: 400;
    line-height: 22px;
    color: #333333;
  }

  .page_flex_des {
    font-size: 22rpx;
    font-weight: 400;
    line-height: 44rpx;
    color: #888888;
  }
}

.page_flex {
  display: flex;
  flex-direction: row;
  justify-content: space-between;

  .page_flex_left {
    position: relative;
    padding-top: 20rpx;
    padding-bottom: 10rpx;
    font-size: 46rpx;
    font-weight: 600;
    color: #000000;
  }

  .page_flex_icon {
    position: absolute;
    right: 60rpx;

    .page_flex_img {
      z-index: 1001;
      width: 400rpx;
      height: 400rpx;
      background-repeat: no-repeat;
      background-position: 100% 100%;
      background-size: 100% 100%;
      @include graph-img('/static/img/ml');
    }
  }

  // .page_flex_right{
  // 	font-weight: 600;
  // 	font-size: 18px;
  // 	color: #000000;
  // 	line-height: 22px;
  // }
}
</style>
