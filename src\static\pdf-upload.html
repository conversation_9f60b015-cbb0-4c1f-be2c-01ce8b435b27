<!doctype html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>PDF上传</title>
    <style>
      * {
        box-sizing: border-box;
        padding: 0;
        margin: 0;
      }

      body {
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        color: #333;
        background-color: #f5f5f5;
      }

      .container {
        max-width: 600px;
        padding: 20px;
        margin: 0 auto;
      }

      .upload-area {
        padding: 40px 20px;
        margin-bottom: 20px;
        text-align: center;
        cursor: pointer;
        background: white;
        border: 2px dashed #007aff;
        border-radius: 12px;
        transition: all 0.3s ease;
      }

      .upload-area:hover {
        background-color: #f8f9fa;
        border-color: #0056b3;
      }

      .upload-icon {
        margin-bottom: 16px;
        font-size: 48px;
        color: #007aff;
      }

      .upload-text {
        margin-bottom: 8px;
        font-size: 18px;
        color: #666;
      }

      .upload-hint {
        font-size: 14px;
        color: #999;
      }

      .file-input {
        display: none;
      }

      .file-info {
        display: none;
        padding: 16px;
        margin-bottom: 20px;
        background: white;
        border-radius: 8px;
      }

      .file-info.show {
        display: block;
      }

      .file-name {
        margin-bottom: 8px;
        font-size: 16px;
        font-weight: 500;
      }

      .file-size {
        font-size: 14px;
        color: #666;
      }

      .progress-bar {
        width: 100%;
        height: 4px;
        margin-top: 12px;
        overflow: hidden;
        background-color: #e9ecef;
        border-radius: 2px;
      }

      .progress-fill {
        width: 0%;
        height: 100%;
        background-color: #007aff;
        transition: width 0.3s ease;
      }

      .upload-btn {
        width: 100%;
        padding: 12px 24px;
        font-size: 16px;
        font-weight: 500;
        color: white;
        cursor: pointer;
        background-color: #007aff;
        border: none;
        border-radius: 8px;
        transition: background-color 0.3s ease;
      }

      .upload-btn:hover {
        background-color: #0056b3;
      }

      .upload-btn:disabled {
        cursor: not-allowed;
        background-color: #6c757d;
      }

      .error-message {
        display: none;
        padding: 12px;
        margin-bottom: 20px;
        color: #721c24;
        background-color: #f8d7da;
        border: 1px solid #f5c6cb;
        border-radius: 8px;
      }

      .error-message.show {
        display: block;
      }
    </style>
  </head>
  <body>
    <div class="container">
      <div class="upload-area" id="uploadArea">
        <div class="upload-icon">📄</div>
        <div class="upload-text">点击上传PDF文件</div>
        <div class="upload-hint">支持PDF格式，最大10MB</div>
        <input
          type="file"
          id="fileInput"
          class="file-input"
          accept=".pdf,application/pdf"
          capture="false"
        />
      </div>

      <div class="error-message" id="errorMessage"></div>

      <div class="file-info" id="fileInfo">
        <div class="file-name" id="fileName"></div>
        <div class="file-size" id="fileSize"></div>
        <div class="progress-bar">
          <div class="progress-fill" id="progressFill"></div>
        </div>
      </div>

      <button class="upload-btn" id="uploadBtn" disabled>上传文件</button>
    </div>

    <script>
      // 全局变量
      let selectedFile = null
      let uploadConfig = null

      // DOM元素
      const uploadArea = document.getElementById('uploadArea')
      const fileInput = document.getElementById('fileInput')
      const fileInfo = document.getElementById('fileInfo')
      const fileName = document.getElementById('fileName')
      const fileSize = document.getElementById('fileSize')
      const progressFill = document.getElementById('progressFill')
      const uploadBtn = document.getElementById('uploadBtn')
      const errorMessage = document.getElementById('errorMessage')

      // 初始化
      document.addEventListener('DOMContentLoaded', function () {
        initEventListeners()
        notifyUniAppReady()
      })

      // 初始化事件监听器
      function initEventListeners() {
        // 点击上传区域
        uploadArea.addEventListener('click', () => {
          // 确保在移动端打开文件管理器而不是相机
          fileInput.setAttribute('capture', 'false')
          fileInput.click()
        })

        // 文件选择
        fileInput.addEventListener('change', handleFileSelect)

        // 上传按钮
        uploadBtn.addEventListener('click', handleUpload)
      }

      // 处理文件选择
      function handleFileSelect(event) {
        const file = event.target.files[0]
        if (file) {
          validateAndSetFile(file)
        }
      }

      // 验证并设置文件
      function validateAndSetFile(file) {
        // 验证文件类型
        if (file.type !== 'application/pdf') {
          showError('请选择PDF格式的文件')
          return
        }

        // 验证文件大小
        const maxSize = uploadConfig?.maxFileSize || 10 * 1024 * 1024 // 默认10MB
        if (file.size > maxSize) {
          showError('文件大小超过限制')
          return
        }

        selectedFile = file
        displayFileInfo(file)
        uploadBtn.disabled = false
        hideError()

        // 通知UniApp
        sendMessageToUniApp({
          type: 'FILE_SELECTED',
          data: {
            name: file.name,
            size: file.size,
            type: file.type,
          },
        })
      }

      // 显示文件信息
      function displayFileInfo(file) {
        fileName.textContent = file.name
        fileSize.textContent = formatFileSize(file.size)
        fileInfo.classList.add('show')
      }

      // 格式化文件大小
      function formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes'
        const k = 1024
        const sizes = ['Bytes', 'KB', 'MB', 'GB']
        const i = Math.floor(Math.log(bytes) / Math.log(k))
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
      }

      // 处理上传
      function handleUpload() {
        if (!selectedFile) {
          showError('请先选择文件')
          return
        }

        if (!uploadConfig?.uploadUrl) {
          showError('上传配置错误')
          return
        }

        uploadBtn.disabled = true
        uploadBtn.textContent = '上传中...'

        uploadFile(selectedFile)
      }

      // 上传文件
      function uploadFile(file) {
        const formData = new FormData()
        formData.append('file', file)

        const xhr = new XMLHttpRequest()

        // 进度监听
        xhr.upload.addEventListener('progress', function (event) {
          if (event.lengthComputable) {
            const progress = (event.loaded / event.total) * 100
            updateProgress(progress)

            sendMessageToUniApp({
              type: 'UPLOAD_PROGRESS',
              data: { progress },
            })
          }
        })

        // 完成监听
        xhr.addEventListener('load', function () {
          if (xhr.status === 200) {
            try {
              const response = JSON.parse(xhr.responseText)
              handleUploadSuccess(response)
            } catch (error) {
              handleUploadError('响应解析失败')
            }
          } else {
            handleUploadError('上传失败: ' + xhr.status)
          }
        })

        // 错误监听
        xhr.addEventListener('error', function () {
          handleUploadError('网络错误')
        })

        // 超时监听
        xhr.addEventListener('timeout', function () {
          handleUploadError('上传超时')
        })

        // 发送请求
        xhr.open('POST', uploadConfig.uploadUrl)

        // 设置请求头
        if (uploadConfig.headers) {
          Object.keys(uploadConfig.headers).forEach((key) => {
            xhr.setRequestHeader(key, uploadConfig.headers[key])
          })
        }

        // 设置超时
        xhr.timeout = uploadConfig.timeout || 30000

        xhr.send(formData)
      }

      // 更新进度
      function updateProgress(progress) {
        progressFill.style.width = progress + '%'
      }

      // 处理上传成功
      function handleUploadSuccess(response) {
        uploadBtn.textContent = '上传成功'
        uploadBtn.style.backgroundColor = '#28a745'

        sendMessageToUniApp({
          type: 'UPLOAD_SUCCESS',
          data: response,
        })
      }

      // 处理上传错误
      function handleUploadError(error) {
        uploadBtn.disabled = false
        uploadBtn.textContent = '上传文件'
        uploadBtn.style.backgroundColor = '#007aff'

        showError(error)

        sendMessageToUniApp({
          type: 'UPLOAD_ERROR',
          data: error,
        })
      }

      // 显示错误
      function showError(message) {
        errorMessage.textContent = message
        errorMessage.classList.add('show')
      }

      // 隐藏错误
      function hideError() {
        errorMessage.classList.remove('show')
      }

      // 通知UniApp就绪
      function notifyUniAppReady() {
        sendMessageToUniApp({
          type: 'WEBVIEW_READY',
        })
      }

      // 发送消息到UniApp
      function sendMessageToUniApp(message) {
        if (window.uni && window.uni.postMessage) {
          window.uni.postMessage({
            data: message,
          })
        } else {
          console.log('发送消息到UniApp:', message)
        }
      }

      // 接收UniApp消息
      window.addEventListener('message', function (event) {
        const message = event.data

        if (message.type === 'INIT_CONFIG') {
          uploadConfig = message.data
          console.log('收到配置:', uploadConfig)
        }
      })
    </script>
  </body>
</html>
