<route lang="json5">
{
  layout: 'default',
  style: {
    navigationBarTitleText: '',
    enablePullDownRefresh: false,
    navigationStyle: 'custom',
  },
}
</route>

<template>
  <view class="bg-img">
    <CustomNavBar>
      <template #left>
        <wd-icon @click="back" name="arrow-left" class="back-button" color="#000" size="20" />
      </template>
      <template #right>
        <wd-img :src="identitySwitchingImg" width="45rpx" height="45rpx" @click="changeIdentFun" />
      </template>
    </CustomNavBar>
    <view class="page_box">
      <view class="page_padding">
        <view class="page_flex_row">
          <view class="page_flex">
            <view class="page_flex_left">求职意向</view>
            <view class="page_flex_icon">
              <view class="page_flex_img"></view>
            </view>
          </view>
          <view class="page_input subText">填写求职意向</view>
        </view>
        <view class="page_flex_row_bottom">
          <view class="page_flex_left">工作类型</view>
          <view class="page_flex_for">
            <view
              class="page_flex_list"
              v-for="(item, index) in tagList"
              :key="index"
              @click="handSelect(index, item)"
            >
              <view :class="selectIndex == index ? 'page_select_border' : 'page_border'">
                {{ item.label }}
              </view>
            </view>
          </view>
        </view>
        <view class="page_flex_row_bottom" @click="hanRegion">
          <view class="page_flex_left_just">
            <view>
              <view class="page_flex_left_row">
                <view class="page_flex_left_color">期望地区</view>
              </view>
              <view class="page_input1">
                <view
                  class="page_input1-text"
                  :class="cityObj.provinceName ? 'selelctColor' : 'nomalColor'"
                  v-if="cityObj.provinceName === cityObj.cityName"
                >
                  {{
                    cityObj.provinceName
                      ? cityObj.provinceName + cityObj.districtName
                      : '请选择期望地区'
                  }}
                </view>
                <view
                  class="page_input1-text"
                  :class="cityObj.provinceName ? 'selelctColor' : 'nomalColor'"
                  v-else
                >
                  {{
                    cityObj.provinceName
                      ? cityObj.provinceName + cityObj.cityName + cityObj.districtName
                      : '请选择期望地区'
                  }}
                </view>
              </view>
            </view>
            <wd-icon name="arrow-right" color="#888888" size="16"></wd-icon>
          </view>
        </view>
        <view class="page_flex_row_bottom" @click="handCareer">
          <view class="page_flex_left_just">
            <view>
              <view class="page_flex_left_row">
                <view class="page_flex_left_color">期望职位</view>
              </view>
              <view class="page_input1">
                <view
                  class="page_input1-text"
                  :class="positionObj.expectedPositions ? 'selelctColor' : 'nomalColor'"
                >
                  {{
                    positionObj.expectedPositions ? positionObj.expectedPositions : '请选择期望职位'
                  }}
                </view>
              </view>
            </view>
            <wd-icon name="arrow-right" color="#888888" size="16"></wd-icon>
          </view>
        </view>
        <view class="">
          <view class="page_flex_row_bottom-1">
            <view>
              <view class="page_flex_left_color">期望薪资</view>

              <view class="page_input1" style="width: 100%">
                <wd-picker
                  ref="pickerPop"
                  :columns="salaryColumns"
                  label=""
                  v-model="salaryValue"
                  :column-change="onSalaryColumnChange"
                  :display-format="salaryDisplayFormat"
                  @confirm="handleSalaryConfirm"
                />
              </view>
            </view>
          </view>
        </view>
        <view class="page_flex_row_bottom" @click="handPosition">
          <view class="page_flex_left_just">
            <view>
              <view class="page_flex_left_row">
                <view class="page_flex_left_color">期望行业</view>
              </view>
              <view class="page_input1">
                <view class="page_input1-text" :class="jobName ? 'selelctColor' : 'nomalColor'">
                  {{ jobName ? jobName : '请选择期望行业' }}
                </view>
              </view>
            </view>
            <wd-icon name="arrow-right" color="#888888" size="16"></wd-icon>
          </view>
        </view>
      </view>
    </view>

    <view class="btn_fixed" @click="submit">
      <view class="btn_box">
        <view class="btn_bg">完成</view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import CustomNavBar from '@/components/CustomNavBar/CustomNavBar.vue'
import { getInfo } from '@/utils/storage'
import { resumeJobIntention } from '@/interPost/biographical'
import { useLoginStore } from '@/store'
import { USER_TYPE, DICT_IDS } from '@/enum'
import identitySwitchingImg from '@/static/mine/business/identity-switching.png'

const { changeIdent } = useChangeIdent()
const { initEaseMobIM } = useEaseMobIM()

// vuex数据
const loginStore = useLoginStore()
// 数据响应式
const cityNameShow = ref('') // 直辖市显示省和市不冲突
const jobType = ref(1) // 工作类型
const cityObj = ref<AnyObject>({}) // 城市
const positionObj = ref<AnyObject>({}) // 职位
const jobObj = ref([]) // 工作
const salary = ref([]) // 薪资
const salaryExpectationStart = ref<any>('') // 薪资开始
const salaryExpectationEnd = ref<any>('') // 薪资结束
const expectedIndustry = ref('') // 行业
const expectedIndustryCode = ref(null)
const jobName = ref('')
const pickerShow = ref(false) // 控制picker显示隐藏
const columnsNum = ref(2) // 设置为两列
const selectedProvinceIndex = ref(0) // 默认选中的省份索引
const selectIndex = ref(0)
const pickerPop = ref()

const { getDictData } = useDictionary()
const tagList = ref([])

// 在script部分添加以下代码
// 修改薪资数据
const salaryData = {
  面议: ['面议'], // 面议单独处理
  '1k': ['2k', '3k', '4k', '5k', '6k'],
  '2k': ['3k', '4k', '5k', '6k', '7k'],
  '3k': ['4k', '5k', '6k', '7k', '8k'],
  '4k': ['5k', '6k', '7k', '8k', '9k'],
  '5k': ['6k', '7k', '8k', '9k', '10k'],
  '6k': ['7k', '8k', '9k', '10k', '11k'],
  '7k': ['8k', '9k', '10k', '11k', '12k'],
  '8k': ['9k', '10k', '11k', '12k', '13k'],
  '9k': ['10k', '11k', '12k', '13k', '14k'],
  '10k': ['11k', '12k', '13k', '14k', '15k'],
  '11k': ['12k', '13k', '14k', '15k', '16k', '17k', '18k', '19k', '20k', '21k'],
  '12k': ['13k', '14k', '15k', '16k', '17k', '18k', '19k', '20k', '21k', '22k'],
  '13k': ['14k', '15k', '16k', '17k', '18k', '19k', '20k', '21k', '22k', '23k'],
  '14k': ['15k', '16k', '17k', '18k', '19k', '20k', '21k', '22k', '23k', '24k'],
  '15k': ['16k', '17k', '18k', '19k', '20k', '21k', '22k', '23k', '24k', '25k'],
  '16k': ['17k', '18k', '19k', '20k', '21k', '22k', '23k', '24k', '25k', '26k'],
  '17k': ['18k', '19k', '20k', '21k', '22k', '23k', '24k', '25k', '26k', '27k'],
  '18k': ['19k', '20k', '21k', '22k', '23k', '24k', '25k', '26k', '27k', '28k'],
  '19k': ['20k', '21k', '22k', '23k', '24k', '25k', '26k', '27k', '28k', '29k'],
  '20k': ['21k', '22k', '23k', '24k', '25k', '26k', '27k', '28k', '29k', '30k'],
  '21k': ['22k', '23k', '24k', '25k', '26k', '27k', '28k', '29k', '30k', '31k'],
  '22k': ['23k', '24k', '25k', '26k', '27k', '28k', '29k', '30k', '31k', '32k'],
  '23k': ['24k', '25k', '26k', '27k', '28k', '29k', '30k', '31k', '32k', '33k'],
  '24k': ['25k', '26k', '27k', '28k', '29k', '30k', '31k', '32k', '33k', '34k'],
  '25k': ['26k', '27k', '28k', '29k', '30k', '31k', '32k', '33k', '34k', '35k'],
  '26k': ['27k', '28k', '29k', '30k', '31k', '32k', '33k', '34k', '35k', '36k'],
  '27k': ['28k', '29k', '30k', '31k', '32k', '33k', '34k', '35k', '36k', '37k'],
  '28k': ['29k', '30k', '31k', '32k', '33k', '34k', '35k', '36k', '37k', '38k'],
  '29k': ['30k', '31k', '32k', '33k', '34k', '35k', '36k', '37k', '38k', '39k'],
  '30k': ['31k', '32k', '33k', '34k', '35k', '36k', '37k', '38k', '39k', '40k'],
  '31k': ['32k', '33k', '34k', '35k', '36k', '37k', '38k', '39k', '40k', '41k'],
  '32k': ['33k', '34k', '35k', '36k', '37k', '38k', '39k', '40k', '41k', '42k'],
  '33k': ['34k', '35k', '36k', '37k', '38k', '39k', '40k', '41k', '42k', '43k'],
  '34k': ['35k', '36k', '37k', '38k', '39k', '40k', '41k', '42k', '43k', '44k'],
  '35k': ['36k', '37k', '38k', '39k', '40k', '41k', '42k', '43k', '44k', '45k'],
  '36k': ['37k', '38k', '39k', '40k', '41k', '42k', '43k', '44k', '45k', '46k'],
  '37k': ['38k', '39k', '40k', '41k', '42k', '43k', '44k', '45k', '46k', '47k'],
  '38k': ['39k', '40k', '41k', '42k', '43k', '44k', '45k', '46k', '47k', '48k'],
  '39k': ['40k', '41k', '42k', '43k', '44k', '45k', '46k', '47k', '48k', '49k'],
  '40k': ['41k', '42k', '43k', '44k', '45k', '46k', '47k', '48k', '49k', '50k'],
  '41k': ['42k', '43k', '44k', '45k', '46k', '47k', '48k', '49k', '50k', '51k'],
  '42k': ['43k', '44k', '45k', '46k', '47k', '48k', '49k', '50k', '51k', '52k'],
  '43k': ['44k', '45k', '46k', '47k', '48k', '49k', '50k', '51k', '52k', '53k'],
  '44k': ['45k', '46k', '47k', '48k', '49k', '50k', '51k', '52k', '53k', '54k'],
  '45k': ['46k', '47k', '48k', '49k', '50k', '51k', '52k', '53k', '54k', '55k'],
  '46k': ['47k', '48k', '49k', '50k', '51k', '52k', '53k', '54k', '55k', '56k'],
  '47k': ['48k', '49k', '50k', '51k', '52k', '53k', '54k', '55k', '56k', '57k'],
  '48k': ['49k', '50k', '51k', '52k', '53k', '54k', '55k', '56k', '57k', '58k'],
  '49k': ['50k', '51k', '52k', '53k', '54k', '55k', '56k', '57k', '58k', '59k'],
  '50k': ['51k', '52k', '53k', '54k', '55k', '56k', '57k', '58k', '59k', '60k'],
}
// 切换身份
const changeIdentFun = async () => {
  changeIdent()
}
const salaryColumns = ref([
  Object.keys(salaryData).map((item) => ({ label: item, value: item })),
  salaryData['面议'].map((item) => ({ label: item, value: item })),
])

const salaryValue = ref([0, 0])

// 修改列变化处理
const onSalaryColumnChange = (picker, values, columnIndex, resolve) => {
  if (columnIndex === 0) {
    const selected = values[0]?.value || '面议'
    if (selected === '面议') {
      picker.setColumnData(1, [{ label: '面议', value: '面议' }])
    } else {
      picker.setColumnData(
        1,
        salaryData[selected].map((item: any) => ({ label: item, value: item })),
      )
    }
    resolve()
  }
}

const salaryDisplayFormat = (items: any) => {
  // 如果选择的是面议，只显示一个"面议"
  if (items[0].label === '面议' && items[1].label === '面议') {
    return '面议'
  }
  // 其他情况正常显示范围
  return items.map((item: any) => item.label).join('-')
}

const handleSalaryConfirm = ({ value }) => {
  if (value[0].indexOf('k') !== -1) {
    salaryExpectationStart.value = value[0].replace('k', '000') // 薪资开始
    salaryExpectationEnd.value = value[1].replace('k', '000') // 薪资结束
  } else {
    salaryExpectationStart.value = 0
    salaryExpectationEnd.value = 0
  }

  console.log(salaryExpectationStart.value, salaryExpectationEnd.value, 'value====')
}
// 返回
const back = () => {
  loginStore.setCity({})
  loginStore.setpositionData({})
  loginStore.setjobArry([])
  uni.navigateBack()
}
const handMoney = () => {
  pickerPop.value.open()
}

// 方法
const handSelect = (index: any, item: any) => {
  selectIndex.value = index
  jobType.value = item.value
}

const hanRegion = () => {
  uni.navigateTo({
    url: '/loginSetting/category/region',
  })
}
// 期待职位
const handCareer = () => {
  uni.navigateTo({
    url: '/loginSetting/category/career',
  })
}

const handPosition = () => {
  uni.navigateTo({
    url: '/loginSetting/category/expPosition',
  })
}

const confirmSure = (e: any) => {
  salary.value = e.value
  salaryExpectationStart.value = e.value[0]
  salaryExpectationEnd.value = e.value[1]
  pickerShow.value = false
}

const togglePicker = () => {
  pickerShow.value = !pickerShow.value
}

const updatePickerData = (provinceIndex: any) => {
  selectedProvinceIndex.value = provinceIndex
}

const onPickerChange = (e) => {
  const [provinceIndex] = e.indexs
  updatePickerData(provinceIndex)
}

const submit = async () => {
  if (!cityObj.value.provinceName) {
    uni.showToast({
      title: '请选择期望地区',
      icon: 'none',
      duration: 3000,
    })
    return
  }

  if (!positionObj.value.expectedPositions) {
    uni.showToast({
      title: '请选择期望职业',
      icon: 'none',
      duration: 3000,
    })
    return
  }
  const res: any = await resumeJobIntention({
    // 省
    provinceName: cityObj.value.provinceName,
    provinceCode: cityObj.value.provinceCode,
    //  城市编
    cityCode: cityObj.value.cityCode,
    cityName: cityObj.value.cityName,
    // 区域
    districtCode: cityObj.value.districtCode,
    districtName: cityObj.value.districtName,
    //= =====
    salaryExpectationStart: salaryExpectationStart.value ? salaryExpectationStart.value : 0,
    salaryExpectationEnd: salaryExpectationEnd.value ? salaryExpectationEnd.value : 0,
    jobType: jobType.value,
    userId: getInfo().userId,
    // 行业
    industry: expectedIndustry.value,
    industryId: expectedIndustryCode.value,
    // 职位
    expectedPositions: positionObj.value.expectedPositions,
    expectedPositionsCode: positionObj.value.expectedPositionsCode,
  })
  // console.log(res, 'res====')
  if (res.code === 0) {
    await initEaseMobIM()
    // 城市
    loginStore.setCity({})
    // 职位
    loginStore.setpositionData({})
    // 职位
    loginStore.setjobArry([])
    uni.reLaunch({
      url: '/pages/home/<USER>',
    })
  } else {
    uni.showToast({
      title: res.msg,
      icon: 'none',
      duration: 3000,
    })
  }
}
onUnload(() => {
  // 城市
  loginStore.setCity({})
  // 职位
  loginStore.setpositionData({})
  // 职位
  loginStore.setjobArry([])
})
// 生命周期
onMounted(() => {
  updatePickerData(0)
})

onLoad(async () => {
  await nextTick()
  const res: any = await getDictData(DICT_IDS.JOB_EXPECTATIONS_PROVINCE)
  const expressiondata = res || res.data
  tagList.value = Object.entries(expressiondata).map(([key, value]) => ({
    value: key,
    label: value,
  }))
  const arryList = [{ active: true, code: 0, name: '不限', childerIndustryData: null }]
  loginStore.setjobArry(arryList)
  updatePickerData(0)
})

onShow(() => {
  // 区域
  cityObj.value = loginStore.cityObj
  // 职位
  positionObj.value = loginStore.positionObj
  // 行业
  const jobList = loginStore.jobObj
  expectedIndustry.value =
    jobList
      .map((item) => item?.name)
      .filter(Boolean)
      .join(',') || '不限'
  expectedIndustryCode.value =
    jobList
      .map((item) => item?.code)
      .filter(Boolean)
      .join(',') || 0
  jobName.value = expectedIndustry.value
})
</script>

<style lang="scss" scoped>
::v-deep .u-button__text {
  font-size: 28rpx !important;
}

::v-deep .u-picker__view {
  height: 500rpx !important;
}
::v-deep .wd-picker__cell {
  padding: 0rpx !important;
}

.selelctColor {
  color: #333333;
}

.nomalColor {
  color: #888888;
}
.page_flex_left_color {
  font-size: 30rpx;
  font-weight: 400;
  line-height: 22px;
  color: #333333;
}
.btn_fixed {
  position: fixed;
  bottom: 80rpx;
  width: 100%;

  .btn_box {
    box-sizing: border-box;
    width: 100%;
    padding: 50rpx;
    padding-bottom: 0;

    .btn_bg {
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 30rpx;
      font-size: 14px;
      font-weight: 500;
      color: #333333;
      background: linear-gradient(90deg, #ffc2c2 0%, #dddcff 100%);
      border-radius: 14px 14px 14px 14px;
    }
  }
}

.page_flex_for {
  display: flex;
  flex-direction: row;

  .page_flex_list {
    display: flex;
    flex-direction: row;
    margin-left: 20rpx;

    .page_select_border {
      padding: 0 22rpx;
      font-size: 22rpx;
      line-height: 44rpx;
      color: #3e9cff;
      background: #f1f1ff;
      border: 1px solid #3e9cff;
      border-radius: 5px 5px 5px 5px;
    }

    .page_border {
      padding: 0 22rpx;
      font-size: 22rpx;
      line-height: 44rpx;
      color: #777777;
      background: #f5f5f5;
      border-radius: 5px 5px 5px 5px;
    }
  }
}

.input_font {
  font-size: 22rpx;
  font-weight: 400;
  line-height: 44rpx;
  color: #777777;
}

.page_input1-text {
  font-size: 28rpx;
  font-weight: 400;
  line-height: 44rpx;
}

.page_box {
  box-sizing: border-box;
  width: 100%;
  padding: 50rpx;
  padding-top: 130rpx;
}

.page_flex_left_just {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  width: 100%;
}
.page_flex_row_bottom-1 {
  width: 100%;
  padding-top: 30rpx;
  padding-bottom: 30rpx;
  border-bottom: 1rpx solid #eaeaea;
}

.page_flex_row_bottom {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  padding-top: 30rpx;
  padding-bottom: 30rpx;
  border-bottom: 1rpx solid #eaeaea;
}

.page_padding {
  padding: 46rpx 50rpx;
  background: #ffffff;
  border-radius: 20px 20px 20px 20px;
  box-shadow: 0px 4px 14px 0px rgba(0, 0, 0, 0.1);
}

.page_input {
  padding-top: 16rpx;
}

.page_input1 {
  padding-top: 10rpx;
}

.page_flex_left_row {
  display: flex;
  flex-direction: row;
  align-items: center;

  .page_flex_des {
    font-size: 22rpx;
    font-weight: 400;
    line-height: 44rpx;
    color: #888888;
  }
}

.page_flex {
  display: flex;
  flex-direction: row;
  justify-content: space-between;

  .page_flex_left {
    font-size: 22px;
    font-weight: 600;
    line-height: 22px;
    color: #000000;
  }

  .page_flex_icon {
    position: absolute;
    top: 20rpx;
    right: 60rpx;

    .page_flex_img {
      z-index: 1001;
      width: 360rpx;
      height: 360rpx;
      background-repeat: no-repeat;
      background-position: 100% 100%;
      background-size: 100% 100%;
      @include graph-img('/static/img/ml');
    }
  }
}
</style>
