<template>
  <view class="flex justify-center">
    <view class="w-482rpx min-h-240rpx pb-20rpx box-unset relative center">
      <view
        class="bg-#ffffff h-158rpx w-full border-rd-16rpx shadow-[0rpx_10rpx_40rpx_0rpx_rgba(0,0,0,0.15)]"
      >
        <view class="flex flex-col items-center gap-18rpx mt--42rpx">
          <view class="center size-96rpx bg-#D8D8D8 rounded-50%">
            <view class="center size-76rpx bg-#4075FF rounded-50%">
              <wd-img :src="iconList[customExts.type]" width="44rpx" height="44rpx" />
            </view>
          </view>
          <text class="c-#000000 text-28rpx font-400">{{ getCustomTips }}</text>
          <!-- <view class="center gap-66rpx">
            <wd-button :round="false" custom-class="!rounded-20rpx">
              <text>点击复制</text>
            </wd-button>
          </view> -->
        </view>
      </view>
    </view>
  </view>
</template>

<script lang="ts" setup>
import type { MixedMessageBody } from '@/ChatUIKit/types'
import sendPhoneImg from '@/ChatUIKit/static/message-custom/send-phone.png'
import sendResumeImg from '@/ChatUIKit/static/message-custom/send-resume.png'
import sendWXImg from '@/ChatUIKit/static/message-custom/wx.png'

type ExtType = Exclude<Api.IM.CustomMessage.ExtType, 'uninterested'>
interface Props {
  msg: MixedMessageBody
}
const props = defineProps<Props>()

const { getIMLoginId } = useIMConversation()
const { userRoleIsBusiness } = useUserInfo()

const iconList: Record<ExtType, string> = {
  resume: sendResumeImg,
  exchange_phone: sendPhoneImg,
  exchange_wechat: sendWXImg,
}

const msgInfo = computed(() => props.msg)
const customExts = computed(
  () =>
    ((msgInfo.value.type === 'custom' && msgInfo.value.customExts) ||
      {}) as Api.IM.CustomMessage.ExtInfo,
)
const formIsMine = computed(() => msgInfo.value.from === getIMLoginId.value)
const getCustomTips = computed(() => {
  const type = customExts.value.type
  const status = customExts.value.status
  const isSender = formIsMine.value
  const messageTypeHandlers: Record<ExtType, () => void> = {
    resume: () => {
      const statusHandlers = {
        /** 发送者 */
        sender: {
          0: userRoleIsBusiness.value ? '等待对方发送简历' : '已发送简历',
          1: userRoleIsBusiness.value ? '对方已同意发送简历' : '对方已同意你的简历请求',
          2: userRoleIsBusiness.value ? '对方拒绝发送简历' : '对方已拒绝你的简历请求',
        },
        /** 接收者 */
        receiver: {
          0: userRoleIsBusiness.value ? '对方发送了简历' : '对方请求你的简历',
          1: userRoleIsBusiness.value ? '你已接受对方的简历' : '你同意了对方的简历请求',
          2: userRoleIsBusiness.value ? '你已拒绝对方的简历' : '你拒绝了对方的简历请求',
        },
      }
      const roleHandler = isSender ? statusHandlers.sender : statusHandlers.receiver
      return roleHandler[status] ?? ''
    },
    exchange_phone: () => {
      const statusHandlers = {
        /** 发送者 */
        sender: {
          0: '已发送交换电话请求',
          1: '对方同意你的交换电话请求',
          2: '对方拒绝你的交换电话请求',
        },
        /** 接收者 */
        receiver: {
          0: '对方请求交换电话',
          1: '你同意了对方的交换电话请求',
          2: '你拒绝了对方的交换电话请求',
        },
      }
      const roleHandler = isSender ? statusHandlers.sender : statusHandlers.receiver
      return roleHandler[status] ?? ''
    },
    exchange_wechat: () => {
      const statusHandlers = {
        /** 发送者 */
        sender: {
          0: '已发送交换微信请求',
          1: '对方同意你的交换微信请求',
          2: '对方拒绝你的交换微信请求',
        },
        /** 接收者 */
        receiver: {
          0: '对方请求交换微信',
          1: '你同意了对方的交换微信请求',
          2: '你拒绝了对方的交换微信请求',
        },
      }
      const roleHandler = isSender ? statusHandlers.sender : statusHandlers.receiver
      return roleHandler[status] ?? ''
    },
  }
  const handler = messageTypeHandlers[type] || (() => '自定义消息')
  return handler()
})
</script>

<style lang="scss" scoped>
//
</style>
