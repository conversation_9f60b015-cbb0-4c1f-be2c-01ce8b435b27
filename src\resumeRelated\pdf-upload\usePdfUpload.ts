import { ref, reactive } from 'vue'

interface PdfUploadConfig {
  maxFileSize: number
  allowedTypes: string[]
  uploadUrl: string
  headers: Record<string, string>
  timeout: number
}

interface UploadResult {
  success: boolean
  data?: any
  error?: string
}

interface WebViewMessage {
  type: string
  data?: any
}

interface UsePdfUploadOptions {
  onLoadingChange?: (loading: boolean) => void
  onErrorChange?: (error: string) => void
  onUrlChange?: (url: string) => void
  onUploadSuccess?: (result: UploadResult) => void
  onUploadError?: (error: string) => void
}

export function usePdfUpload(options: UsePdfUploadOptions = {}) {
  const { onLoadingChange, onErrorChange, onUrlChange, onUploadSuccess, onUploadError } = options

  // 状态管理
  const isInitialized = ref(false)

  // 配置信息
  const config = reactive<PdfUploadConfig>({
    maxFileSize: 10 * 1024 * 1024, // 10MB
    allowedTypes: ['application/pdf'],
    uploadUrl: '',
    headers: {},
    timeout: 30000,
  })

  // 初始化WebView
  const initWebView = () => {
    try {
      // 设置加载状态
      onLoadingChange?.(true)
      onErrorChange?.('')

      // 使用静态HTML文件
      const staticUrl = '/static/pdf-upload.html'
      onUrlChange?.(staticUrl)
      isInitialized.value = true
    } catch (error) {
      console.error('初始化WebView失败:', error)
      onErrorChange?.('初始化失败，请重试')
    }
  }

  // 处理WebView消息
  const handleWebViewMessage = (event: any) => {
    try {
      const message: WebViewMessage = event.detail?.data?.[0] || event.detail

      if (!message || !message.type) {
        console.warn('无效的WebView消息:', message)
        return
      }

      console.log('收到WebView消息:', message)

      switch (message.type) {
        case 'WEBVIEW_READY':
          handleWebViewReady()
          break

        case 'FILE_SELECTED':
          handleFileSelected(message.data)
          break

        case 'UPLOAD_PROGRESS':
          handleUploadProgress(message.data)
          break

        case 'UPLOAD_SUCCESS':
          handleUploadSuccess(message.data)
          break

        case 'UPLOAD_ERROR':
          handleUploadError(message.data)
          break

        case 'ERROR':
          handleWebViewError(message.data)
          break

        default:
          console.warn('未知的消息类型:', message.type)
      }
    } catch (error) {
      console.error('处理WebView消息失败:', error)
    }
  }

  // 处理WebView加载完成
  const handleWebViewLoad = () => {
    console.log('WebView加载完成')
    onLoadingChange?.(false)
  }

  // 处理WebView错误
  const handleWebViewError = (error: any) => {
    console.error('WebView错误:', error)
    onErrorChange?.('页面加载失败，请重试')
    onLoadingChange?.(false)
  }

  // 处理WebView就绪
  const handleWebViewReady = () => {
    console.log('WebView就绪')
    // 发送配置到WebView
    sendMessageToWebView({
      type: 'INIT_CONFIG',
      data: config,
    })
  }

  // 处理文件选择
  const handleFileSelected = (data: any) => {
    console.log('文件已选择:', data)
    // 可以在这里添加文件验证逻辑
  }

  // 处理上传进度
  const handleUploadProgress = (data: any) => {
    console.log('上传进度:', data.progress)
    // 发送进度信息给父组件
    uni.$emit('pdfUploadProgress', data.progress)
  }

  // 处理上传成功
  const handleUploadSuccess = (data: UploadResult) => {
    console.log('上传成功:', data)
    onUploadSuccess?.(data)

    // 发送结果给父组件
    uni.$emit('pdfUploadResult', data)

    // 显示成功提示
    uni.showToast({
      title: '上传成功',
      icon: 'success',
    })
  }

  // 处理上传错误
  const handleUploadError = (error: string) => {
    console.error('上传失败:', error)
    onUploadError?.(error)

    // 发送错误结果给父组件
    uni.$emit('pdfUploadResult', {
      success: false,
      error,
    })

    // 显示错误提示
    uni.showToast({
      title: error || '上传失败',
      icon: 'error',
    })
  }

  // 重试加载
  const retryLoad = () => {
    onErrorChange?.('')
    initWebView()
  }

  // 清理资源
  const cleanup = () => {
    isInitialized.value = false
  }

  // 设置上传配置
  const setUploadConfig = (newConfig: Partial<PdfUploadConfig>) => {
    Object.assign(config, newConfig)
    console.log('更新上传配置:', config)
  }

  // 发送消息到WebView
  const sendMessageToWebView = (message: WebViewMessage) => {
    // 通过WebView的postMessage方法发送消息
    if ((window as any).uni && (window as any).uni.postMessage) {
      ;(window as any).uni.postMessage({
        data: message,
      })
    }
  }

  return {
    // 状态
    isInitialized,
    config,

    // 方法
    initWebView,
    handleWebViewMessage,
    handleWebViewLoad,
    handleWebViewError,
    retryLoad,
    cleanup,
    setUploadConfig,
    sendMessageToWebView,
  }
}
