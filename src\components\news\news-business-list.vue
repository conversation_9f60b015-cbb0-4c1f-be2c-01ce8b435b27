<template>
  <view class="flex flex-col gap-34rpx">
    <view
      class="flex items-center gap-20rpx"
      v-for="(item, key) in conversationList"
      :key="`news-list-${key}`"
      @click="toChatPage(item.conversationId)"
    >
      <view class="size-68rpx">
        <wd-img width="100%" height="100%" round :src="item?.avatarurl" />
      </view>
      <view class="flex flex-col gap-4rpx flex-1">
        <view class="flex items-center gap-4rpx">
          <text class="c-#555555 text-28rpx font-500 flex-1 line-clamp-1">
            {{ item?.nickname }}
          </text>
          <text class="c-#888888 text-22rpx">
            {{
              formatSmartTime(
                item.lastMessage && 'time' in item.lastMessage ? item.lastMessage.time : '',
              )
            }}
          </text>
        </view>
        <view class="flex items-center">
          <text class="line-clamp-1 c-#888888 text-22rpx ml--10rpx flex-1">
            {{ getLastTypeMessage(item.lastMessage as any) }}
          </text>
          <view class="bg-#FF3333 size-32rpx center border-rd-50%" v-if="!!item.unReadCount">
            <text class="text-20rpx c-#ffffff">
              {{ item.unReadCount > 99 ? '99+' : item.unReadCount }}
            </text>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script lang="ts" setup>
import { formatSmartTime } from '@/utils'

const $emits = defineEmits<{
  (e: 'chat', id: string): void
}>()

const { conversationList, getLastTypeMessage } = useIMConversation()

const toChatPage = (id: string) => {
  $emits('chat', id)
}
</script>

<style lang="scss" scoped>
//
</style>
