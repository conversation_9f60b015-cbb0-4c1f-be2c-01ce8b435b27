<route lang="json5">
{
  layout: 'default',
  style: {
    navigationBarTitleText: '',
    enablePullDownRefresh: false,
    navigationStyle: 'custom',
  },
}
</route>
<template>
  <z-paging layout-only :paging-style="pageStyle">
    <template #top>
      <CustomNavBar title="" />
    </template>
    <view class="setting">
      <view class="tabs-center-wrap">
        <wd-tabs
          v-model="currentTab"
          color="#000000"
          inactive-color="#000000"
          @change="handleChangeTab"
        >
          <wd-tab
            v-for="item in commonPhrasesTab"
            :key="item.name"
            :title="item.label"
            :name="item.name"
          />
        </wd-tabs>
      </view>
      <view v-if="currentTab === '1'">
        <!-- 常用语列表 -->
        <view v-for="(item, idx) in commonList" :key="idx" class="phrase-card">
          <view class="phrase-content">{{ item.text }}</view>
          <view class="phrase-icon-wrap">
            <image
              src="../../../../static/setting/sorting_icon.png"
              mode="widthFix"
              class="phrase-icon"
            />
          </view>
        </view>
      </view>
      <view v-else>
        <!-- 招呼语列表 -->
        <view v-for="(item, idx) in greetList" :key="idx" class="phrase-card-greet">
          <view class="phrase-content-greet">{{ item.text }}</view>
          <view class="phrase-radio-wrap">
            <wd-radio-group
              v-model="defaultIndex"
              shape="dot"
              :checked-color="'#ff9191'"
              inline
              @change="handleDefaultChange"
            >
              <wd-radio
                :value="idx"
                label="设置为默认"
                size="28rpx"
                :class="{ 'default-text': item.isDefault }"
              >
                设为默认
              </wd-radio>
            </wd-radio-group>
          </view>
        </view>
      </view>
    </view>
    <template #bottom>
      <view class="btn-fixed">
        <view class="btn_box" @click="goCommonGreetings">
          <view class="btn_bg">添加常用语</view>
        </view>
      </view>
    </template>
  </z-paging>
</template>

<script setup lang="ts">
import CustomNavBar from '@/components/CustomNavBar/CustomNavBar.vue'
const { pageStyle } = usePaging({
  style: {
    background: 'linear-gradient( 125deg, #FFDEDE 0%, #EBEFFA 20%, #FFFFFF 100%)',
  },
})

const currentTab = ref('2')
const commonPhrasesTab = ref([
  { name: '1', label: '常用语' },
  { name: '2', label: '招呼语' },
])
const commonList = ref([
  {
    text: '老板你好！非常想加入你们，可以看下我的资料。期待回复。',
  },
  { text: '老板你好！非常想加入你们，可以看下我的资料。期待回复。' },
  { text: '老板你好！非常想加入你们，可以看下我的资料。期待回复。' },
])

const greetList = ref([
  { text: '老板你好！非常想加入你们，可以看下我的资料。期待回复。', isDefault: false },
  { text: '老板你好！非常想加入你们，可以看下我的资料。期待回复。', isDefault: true },
])

// 默认选中的索引
const defaultIndex = ref(1) // 初始设置为第二个选项为默认

const handleChangeTab = (e: any) => {
  console.log(e)
}

const handleDefaultChange = (value: number) => {
  // 更新所有选项的默认状态
  greetList.value.forEach((item, idx) => {
    item.isDefault = idx === value
  })
}

const goCommonGreetings = () => {
  uni.navigateTo({
    url: '/sub_business/pages/setting/model/message/CommonGreetings',
  })
}
</script>
<style scoped lang="scss">
.setting {
  padding: 0rpx 24rpx 0rpx 24rpx;
  .phrase-card {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 24rpx 24rpx 16rpx 24rpx;
    margin-top: 48rpx;
    background: #fff;
    border-radius: 16rpx;
    box-shadow: 0 4rpx 16rpx 0 rgba(0, 0, 0, 0.04);
    .phrase-content {
      flex: 1;
      font-size: 28rpx;
      line-height: 1.6;
      color: #333;
      word-break: break-all;
    }

    .phrase-icon-wrap {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 64rpx;
      height: 64rpx;
      background: #e1ebff;
      border-radius: 5rpx;

      .phrase-icon {
        width: 32rpx;
        height: 32rpx;
      }
    }
  }

  .phrase-card-greet {
    padding: 24rpx 24rpx 16rpx 24rpx;
    margin-top: 48rpx;
    background: #fff;
    border-radius: 16rpx;
    box-shadow: 0 4rpx 16rpx 0 rgba(0, 0, 0, 0.04);
    .phrase-content-greet {
      flex: 1;
      font-size: 28rpx;
      line-height: 1.6;
      color: #333;
      word-break: break-all;
    }
    .phrase-radio-wrap {
      margin: 24rpx 0;
    }
  }
}
.btn-fixed {
  position: fixed;
  right: 0;
  bottom: 48rpx;
  left: 0;
  z-index: 10;
  box-sizing: border-box;
  display: flex;
  justify-content: center;
  padding: 40rpx 40rpx;
  .btn_box {
    box-sizing: border-box;
    width: 100%;
    .btn_bg {
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 20rpx 0rpx;
      font-size: 28rpx;
      font-weight: 500;
      color: #333;
      background: linear-gradient(90deg, #ffc2c2 0%, #dddcff 100%);
      border-radius: 14px 14px 14px 14px;
    }
  }
}

.tabs-center-wrap {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40%;
  margin-left: 28%;
  background: transparent;
}

:deep(.wd-tabs) {
  background: transparent;
  .wd-tabs__nav {
    background: transparent;
  }
  .wd-tabs__nav-item {
    font-size: 32rpx;
    color: #9e9e9e;
    &.is-active {
      font-size: 32rpx;
      color: #333;
    }
  }
  .wd-tabs__line {
    width: 75rpx;
    background: #ff9191;
  }
}
:deep(.wd-radio__label) {
  font-size: 28rpx;
  color: #666;
}

:deep(.wd-radio.is-checked .wd-radio__label) {
  color: #ff9191;
}
</style>
