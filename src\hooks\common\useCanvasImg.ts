import { formatDateDayFmt } from '@/utils/common'
export default function useCanvasImg() {
  // 辅助函数：画下划线并写变量内容
  function drawUnderlineText(ctx, text, x, y, width) {
    ctx.beginPath()
    ctx.moveTo(x, y)
    ctx.lineTo(x + width, y)
    ctx.setStrokeStyle('#000')
    ctx.setLineWidth(2)
    ctx.stroke()
    ctx.setFontSize(36)
    ctx.setFillStyle('#000')
    ctx.setTextAlign('center')
    ctx.fillText(text || '', x + width / 2, y - 8)
  }

  const downloadTemplate = (companyName, idCard) => {
    const width = 2480
    const height = 3508
    // #ifdef MP-WEIXIN || APP-PLUS
    const ctx = uni.createCanvasContext('a4Template')
    // 白底
    ctx.setFillStyle('#fff')
    ctx.fillRect(0, 0, width, height)
    // 标题
    ctx.setFillStyle('#000')
    ctx.setFontSize(80)
    ctx.setTextAlign('center')
    ctx.fillText('任 职 证 明', width / 2, 500)
    ctx.setFontSize(40)
    ctx.fillText('PROOF OF EMPLOYMENT', width / 2, 600)
    // 正文（整体居中排布）
    ctx.setFontSize(36)
    ctx.setTextAlign('left')
    // 计算整行宽度以便整体居中
    const marginTop = 1100
    const text1 = '兹证明'
    const text2 = '，身份证号：'
    const text3 = '，在我公司任职，'
    const underlineWidth1 = 320
    const underlineWidth2 = 420
    // 计算各部分宽度
    ctx.setFontSize(36)
    const measure1 = ctx.measureText(text1).width
    const measure2 = ctx.measureText(text2).width
    const measure3 = ctx.measureText(text3).width
    // 总宽度
    const totalWidth = measure1 + underlineWidth1 + measure2 + underlineWidth2 + measure3
    // 起始x
    const startX = (width - totalWidth) / 2
    let x = startX
    ctx.fillText(text1, x, marginTop)
    x += measure1
    drawUnderlineText(ctx, companyName || '', x, marginTop, underlineWidth1)
    x += underlineWidth1
    ctx.fillText(text2, x, marginTop)
    x += measure2
    drawUnderlineText(ctx, idCard || '', x, marginTop, underlineWidth2)
    x += underlineWidth2
    ctx.fillText(text3, x, marginTop)
    // 第二行说明，居中
    ctx.setTextAlign('center')
    ctx.fillText(
      '此证明仅限易直聘平台招聘使用，不作为任何形式的担保证明文件，复印无效。',
      width / 2,
      marginTop + 70,
    )
    // 第三行，居中
    ctx.fillText('特此证明。', width / 2, marginTop + 170)
    // 落款
    ctx.setTextAlign('right')
    // 画落款下划线
    const marginX = 420
    drawUnderlineText(ctx, '', width - marginX - 250, height - 500, 250)
    ctx.fillText('（加盖公章）', width - marginX, height - 500)
    // 日期
    const dateObj = formatDateDayFmt()
    ctx.setTextAlign('right')
    // 年
    drawUnderlineText(ctx, dateObj.year, width - marginX - 500, height - 400, 100)
    ctx.fillText('年', width - marginX - 390, height - 400)
    // 月
    drawUnderlineText(ctx, dateObj.month, width - marginX - 360, height - 400, 70)
    ctx.fillText('月', width - marginX - 280, height - 400)
    // 日
    drawUnderlineText(ctx, dateObj.day, width - marginX - 250, height - 400, 70)
    ctx.fillText('日', width - marginX - 170, height - 400)
    ctx.draw(false, () => {
      uni.canvasToTempFilePath({
        canvasId: 'a4Template',
        width,
        height,
        destWidth: width,
        destHeight: height,
        success: function (res) {
          uni.saveImageToPhotosAlbum({
            filePath: res.tempFilePath,
            success: function () {
              uni.showToast({ title: '已下载', icon: 'success' })
            },
            fail: function () {
              uni.showToast({ title: '下载失败', icon: 'none' })
            },
          })
        },
        fail: function () {
          uni.showToast({ title: '生成图片失败', icon: 'none' })
        },
      })
    })
    // #endif
  }
  return {
    downloadTemplate,
  }
}
