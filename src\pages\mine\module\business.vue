<template>
  <z-paging :paging-style="pageStyle" layout-only>
    <template #top>
      <wd-navbar :bordered="false" safe-area-inset-top placeholder custom-class="!bg-transparent">
        <template #right>
          <view class="flex items-center" @click="goSeeting">
            <wd-img :src="setupImg" width="58rpx" height="58rpx" />
          </view>
        </template>
      </wd-navbar>
    </template>
    <view class="px-50rpx">
      <view
        class="flex items-center py-32rpx border-b-1px border-b-solid border-b-[#BDBDBD]"
        @click="goPersonalInfo"
      >
        <view class="flex items-center gap-24rpx flex-1">
          <wd-img width="110rpx" height="110rpx" round :src="myObj.hrPositionUrl" />

          <view class="flex flex-col gap-14rpx">
            <view class="flex items-center line-clamp-1">
              <text class="c-#333333 text-32rpx font-500">{{ myObj.trueName }}</text>
              <text v-if="myObj?.hrPosition">&nbsp;·&nbsp;</text>
              <text class="c-#555555 text-28rpx" v-if="myObj?.hrPosition">
                {{ myObj.hrPosition ? myObj.hrPosition : '' }}
              </text>
            </view>
            <text class="c-#333333 text-28rpx line-clamp-1">
              {{ truncateText(myObj.companyName, 14) }}
            </text>
          </view>
        </view>
        <wd-icon name="arrow-right" color="#555555" size="40rpx" />
      </view>
      <view class="flex flex-col gap-60rpx mt-32rpx">
        <view class="grid grid-cols-4 mx--50rpx">
          <view
            class="flex flex-col items-center gap-10px"
            v-for="(item, key) in newsList"
            :key="`news-${key}`"
            @click="goNewsPage(key)"
          >
            <text class="c-#555555 text-24rpx">{{ item.name }}</text>
            <text class="c-#333333 text-36rpx font-bold">{{ item.num }}</text>
          </view>
        </view>
        <view class="grid grid-cols-4 mx--10rpx gap-30rpx">
          <view
            class="flex flex-col items-center gap-10px"
            v-for="(item, key) in selectList"
            :key="`select-${key}`"
            @click="changeHandel(key)"
          >
            <view class="relative w-58rpx h-58rpx flex p-6rpx">
              <wd-img width="100%" height="100%" :src="`/static/mine/business/${item.icon}.png`" />
            </view>
            <text class="c-#000000 text-24rpx">{{ item.title }}</text>
          </view>
        </view>
        <view
          class="h-196rpx bg-cover bg-no-repeat bg-center border-rd-40rpx flex flex-col justify-center box-border pl-32rpx pr-134rpx gap-16rpx"
          :style="{ backgroundImage: `url(${bombCardBg})` }"
        >
          <wd-img :src="bombCardTitle" width="154rpx" height="46rpx" />
          <text class="text-22rpx c-#000000 line-clamp-2">
            使用规则使用规则使用规则使用规则使用
          </text>
        </view>
      </view>
    </view>
    <template #bottom>
      <view class="m-b-20rpx">
        <CommonLink></CommonLink>
      </view>
      <customTabbar name="mine" />
    </template>
  </z-paging>
  <orbital-menu :items="selectList" />
</template>

<script lang="ts" setup>
import { truncateText } from '@/utils/util'
import CommonLink from '@/components/CommonLink/CommonLinkMy.vue'
import customTabbar from '@/components/common/custom-tabbar.vue'
import orbitalMenu from '@/components/common/orbital-menu.vue'
import { myTotalList, hruserInfo } from '@/service/hrUser'
import identitySwitchingImg from '@/static/mine/business/identity-switching.png'
import setupImg from '@/static/common/setup.png'
import bombCardTitle from '@/static/mine/business/bomb-card-title.png'
import bombCardBg from '@/static/mine/business/bomb-card-bg.png'
import { logout } from '@/interPost/login'
import { clearStorageSync } from '@/utils/storage'
import { useLoginStore } from '@/store'
import ItemContainer from '@/ChatUIKit/modules/Chat/components/MessageInputToolBar/itemContainer.vue'

defineOptions({
  name: 'MineBusiness',
})
const { pageStyle } = usePaging({
  style: {
    background: 'linear-gradient( 125deg, #FFDEDE 0%, #EBEFFA 20%, #FFFFFF 100%)',
  },
})
// 我的信息
const myObj = ref<AnyObject>({})
// vuex数据
const loginStore = useLoginStore()
const newsList = reactive([
  {
    name: '面试',
    num: 0,
  },
  {
    name: '沟通过',
    num: 0,
  },
  {
    name: '被收藏',
    num: 0,
  },
  {
    name: '不合适',
    num: 0,
  },
])
const selectList = reactive([
  {
    icon: 'job-data',
    title: '发布岗位',
  },
  {
    icon: 'company-homepage',
    title: '公司信息',
  },
  {
    icon: 'address-management',
    title: '地址管理',
  },
  {
    icon: 'my-props',
    title: '我的道具',
  },
  {
    icon: 'rules-center',
    title: '规则中心',
  },
  {
    icon: 'wallet-invoices',
    title: '钱包发票',
  },
  {
    icon: 'my-customer-service',
    title: '我的客服',
  },
  {
    icon: 'privacy-policy',
    title: '隐私协议',
  },
])
// hr基本信息列表
const myList = async () => {
  const res: any = await hruserInfo()
  console.log(res, '===========')
  if (res.code === 0) {
    myObj.value = res.data
    if (res.data.hrPositionUrl) {
      myObj.value.hrPositionUrl = res.data.hrPositionUrl
    } else {
      myObj.value.hrPositionUrl =
        myObj.value.gender === 1 ? '/static/header/hrheader1.png' : '/static/header/hrheader2.png'
    }
  }
}
const goNewsPage = (index: number) => {
  if (index === 0) {
    uni.navigateTo({
      url: '/sub_business/pages/interview/index',
    })
  }
  if (index === 1) {
    uni.navigateTo({
      url: '/sub_business/pages/communicate/index',
    })
  }
  if (index === 2) {
    uni.navigateTo({
      url: '/sub_business/pages/tucked/index',
    })
  }
  if (index === 3) {
    uni.navigateTo({
      url: '/sub_business/pages/inappropriate/index',
    })
  }
}

const goSeeting = () => {
  uni.navigateTo({
    url: '/sub_business/pages/setting/index',
  })
}
// 去我的信息
const goPersonalInfo = () => {
  uni.navigateTo({
    url: '/sub_business/pages/myInfo/index',
  })
}
// 获取简历沟通
const myTotalListFun = async () => {
  const res: any = await myTotalList()
  if (res.code === 0) {
    newsList[0].num = res.data.interviewCount
    newsList[1].num = res.data.interactCount
    newsList[2].num = res.data.beenCollectCount
    newsList[3].num = res.data.unInterestCount
  }
}

// 支付
const changeHandel = (index: number) => {
  console.log(index, 'index===')
  if (index === 0) {
    uni.navigateTo({
      url: '/sub_business/pages/release/index',
    })
  }
  if (index === 1) {
    uni.navigateTo({
      url: '/sub_business/pages/company/index',
    })
  }
  if (index === 2) {
    uni.navigateTo({
      url: '/sub_business/pages/AddressCenter/index',
    })
  }
  if (index === 4) {
    uni.navigateTo({
      url: '/sub_business/pages/setting/model/PrivacyAgreement',
    })
  }
  if (index === 6) {
    uni.navigateTo({
      url: '/sub_business/pages/service/index',
    })
  }
}
const mineShow = () => {
  myList()
  myTotalListFun()
}
defineExpose({
  mineShow,
})
</script>

<style lang="scss" scoped>
//
</style>
