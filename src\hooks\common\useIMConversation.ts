import { CommonUtil } from 'wot-design-uni'
import pLimit from 'p-limit'
import { chatSDK } from '@/ChatUIKit/sdk'
import { EasemobChat } from 'easemob-websdk'
import { IM_LISTENER_TYPE, USER_TYPE } from '@/enum'
import { imSessionRecordSaveSession } from '@/service/imSessionRecord'
import { resumeQueryMyFileResumeList } from '@/service/resume'
import {
  exchangeResumeRecordSendResume,
  exchangeResumeRecordBatchSendResume,
  exchangeResumeRecordBatchSendResumeCallback,
} from '@/service/exchangeResumeRecord'
import { exchangeNumberExchange } from '@/service/exchangeNumber'
import { exchangeCodeExchange } from '@/service/exchangeCode'
import type { UserInfoWithPresence, UIKITConversationItem } from '@/ChatUIKit/types'
import type { positionInfoQueryIMCardInfoByIdInt } from '@/service/positionInfo/types'
import type { exchangeCodeExchangeDataInt } from '@/service/exchangeCode/types'
import type {
  batchSendResumeDataInt,
  BatchSendMessageListInt,
} from '@/service/exchangeResumeRecord/types'

interface SendResult {
  recipient: string
  status: 'fulfilled' | 'rejected'
  reason?: any
}
type ConversationWithUserInfo = UIKITConversationItem & EasemobChat.UpdateOwnUserInfoParams
interface CustomMessage
  extends Omit<EasemobChat.CreateCustomMsgParameters, 'type' | 'chatType' | 'customExts'> {
  customExts: Api.IM.CustomMessage.ExtInfo
}
interface SendMassResumeMessageParams extends Pick<batchSendResumeDataInt, 'greeting'> {
  sendList: BatchSendMessageListInt[]
}
const chatType: EasemobChat.ChatType = 'singleChat'
const customCardInfo = ref<positionInfoQueryIMCardInfoByIdInt>({})
const conversationList = ref<ConversationWithUserInfo[]>([])
const totalUnreadCount = ref(0)
export const useIMConversation = () => {
  const { userIntel } = useUserInfo()
  const lastUpdatedTime = ref(0)
  /**
   * 加载会话列表
   * @param forceRefresh 是否强制刷新
   */
  const loadConversations = async (forceRefresh = false) => {
    if (!uni.$UIKit) return
    const now = Date.now()
    if (!forceRefresh && now - lastUpdatedTime.value < 5000) return
    try {
      await uni.$UIKit.convStore.getConversationList()
      totalUnreadCount.value = uni.$UIKit.convStore.totalUnreadCount
      const conversationStoreList = uni.$UIKit.convStore.conversationList
      const conversationIds = conversationStoreList.map((item) => item.conversationId)
      await uni.$UIKit.appUserStore.getUsersInfoFromServer({
        userIdList: conversationIds,
      })
      const userInfo = uni.$UIKit.appUserStore.appUserInfo
      conversationList.value = conversationStoreList.map((item) => {
        const { conversationId } = item
        const user = userInfo.get(conversationId) ?? {}
        return {
          ...item,
          ...user,
        }
      })
      // #ifdef APP-PLUS
      plus.runtime.setBadgeNumber(totalUnreadCount.value)
      // #endif
      lastUpdatedTime.value = now
    } catch (error) {
      console.error('加载会话列表失败', error)
    }
  }
  /** 监听新消息以更新未读计数 */
  const listenForNewMessages = () => {
    if (!uni.$UIKit) return
    uni.$UIKit.getChatConn().addEventHandler(IM_LISTENER_TYPE.RECEIVE_MESSAGE, {
      onTextMessage: async (msg) => {
        loadConversations(true)
      },
      onImageMessage: async () => {
        loadConversations(true)
      },
      onCustomMessage: async () => {
        loadConversations(true)
      },
      onReceivedMessage: CommonUtil.debounce(() => {
        loadConversations(true)
      }, 300),
      onReadMessage: async (msg) => {
        console.log('收到已读回执', msg)
      },
      onDeliveredMessage: async (msg) => {
        console.log('收到已送达回执', msg)
      },
    })
  }
  /** 创建本地通知栏消息 */
  const createLocalNotification = (options: UniNamespace.CreatePushMessageOptions) => {
    uni.createPushMessage({
      ...options,
    })
  }
  /** 获取im用户配置信息 */
  const getIMUserInfo = (userId: string) => {
    return uni.$UIKit.appUserStore.getUserInfoFromStore(userId)
  }
  /** 获取自己的im信息 */
  const getIMUserInfoSelf = computed(() => uni.$UIKit.appUserStore.getSelfUserInfo())
  /** 获取当前会话信息 */
  const getCoversationInfo = computed(() => uni.$UIKit.convStore.currConversation)
  /** 获取当前im登录实例 */
  const getIMLoginId = computed(() => uni.$UIKit.getChatConn().user ?? '')
  /** 根据类型获取最后一条消息 */
  const getLastTypeMessage = (last: EasemobChat.MessageBody & Record<string, never>) => {
    const messageHandlers = {
      txt: (msg: EasemobChat.TextMsgBody) => msg.msg || '',
      img: () => '[图片]',
      custom: (msg: EasemobChat.CustomMsgBody) => {
        const customExts = msg.customExts || {}
        const customTypeHandlers: Record<
          Exclude<Api.IM.CustomMessage.ExtType, 'uninterested'>,
          string
        > = {
          resume: '[简历]',
          exchange_phone: '[手机号]',
          exchange_wechat: '[微信号]',
        }
        return customTypeHandlers[customExts.type] || '自定义消息'
      },
      _default: (msg: any) => `[${msg.type || '未知'}消息]`,
    }
    const handler = messageHandlers[last.type] || messageHandlers._default
    return handler(last)
  }
  const hasConversation = (conversationId: string) => {
    return !!uni.$UIKit.convStore.getConversationById(conversationId)
  }
  /** 发送即时文本消息 */
  const sendIMTextMessage = async (to: string, msg: string) => {
    if (!hasConversation(to)) {
      const createMsg = chatSDK.message.create({
        type: 'txt',
        to,
        chatType,
        msg,
      })
      uni.$UIKit.messageStore.sendMessage(createMsg)
      await CommonUtil.pause(500)
    }
  }
  /** 发送自定义消息 */
  const sendCustomMessage = async (message: CustomMessage) => {
    const { to, ...params } = message
    const createMsg = chatSDK.message.create({
      type: 'custom',
      to,
      chatType,
      ...params,
    })
    uni.$UIKit.messageStore.sendMessage(createMsg)
  }
  /** 发送微信号 */
  const sendCustomWeChatCodeMessage = async (to: string, exchange: exchangeCodeExchangeDataInt) => {
    try {
      const { data } = await exchangeCodeExchange(exchange)
      await sendCustomMessage({
        to,
        customEvent: '',
        customExts: {
          type: 'exchange_wechat',
          status: 0,
          exchange_wechat_record_id: data,
        },
      })
    } catch (error) {
      return Promise.reject(new Error('发送微信号失败'))
    }
  }
  /** 发送手机号 */
  const sendCustomPhoneMessage = async (to: string, userId: number) => {
    try {
      const { data } = await exchangeNumberExchange({
        phone: userIntel.value.phone,
        userId,
      })
      await sendCustomMessage({
        to,
        customEvent: '',
        customExts: {
          type: 'exchange_phone',
          status: 0,
          exchange_phone_record_id: data,
        },
      })
    } catch (error) {
      return Promise.reject(new Error('发送手机号失败'))
    }
  }
  /** 发送/索要简历 */
  const sendCustomResumeMessage = async (to: string, post: AnyObject) => {
    try {
      const {
        id: positionId,
        hxUserInfoVO: { userId: hrId },
      } = post
      const { data } = await resumeQueryMyFileResumeList()
      if (!data || !data.length) {
        return Promise.reject(new Error('请先完善简历信息'))
      }
      const [{ fileId: attachmentId }] = data
      const { data: resumeId } = await exchangeResumeRecordSendResume({
        attachmentId,
        hrId,
        positionId,
      })
      await sendCustomMessage({
        to,
        customEvent: '',
        customExts: {
          type: 'resume',
          status: 0,
          exchange_resume_record_id: resumeId,
        },
      })
    } catch {
      return Promise.reject(new Error('投递失败'))
    }
  }
  /** 打招呼 */
  const sendGreetingMessage = async (to: string, post: AnyObject = {}, chat = true) => {
    try {
      const {
        id: positionId,
        positionName,
        companyId,
        hxUserInfoVO: { userId },
      } = post
      // 消息模板和会话参数配置
      const roleConfig = {
        [USER_TYPE.HR]: {
          getMessage: (name?: string) =>
            name
              ? `您好，我们正在诚聘${name}，有兴趣聊聊吗？`
              : 'Hi，您好，我们正在诚聘，期待您的加入！',
          getSessionParams: {
            companyId: userIntel.value.companyId,
            hrUserId: userIntel.value.userId,
            positionId,
            userId,
          },
        },
        [USER_TYPE.APPLICANT]: {
          getMessage: (name?: string) =>
            name
              ? `您好，希望和您沟通下${name}`
              : '您好，我对您发布的职位很感兴趣，希望能进一步了解！',
          getSessionParams: {
            companyId,
            hrUserId: userId,
            positionId,
            userId: userIntel.value.userId,
          },
        },
      }
      const userType = userIntel.value.type
      const { getMessage, getSessionParams } = roleConfig[userType]
      await imSessionRecordSaveSession(getSessionParams, {
        custom: { catch: true },
      })
      await sendIMTextMessage(to, getMessage(positionName))
      if (chat) {
        const url = CommonUtil.buildUrlWithParams('/ChatUIKit/modules/Chat/index', {
          type: 'singleChat',
          id: to,
        })
        uni.navigateTo({ url })
      }
    } catch (error) {
      console.error('发送打招呼消息失败', error)
      throw error
    }
  }
  /** 发送简历 */
  const sendResumeMessage = async (to: string, post: AnyObject = {}) => {
    const TOAST_DURATION = 2000
    try {
      await sendCustomResumeMessage(to, post)
      await sendGreetingMessage(to, post, false)
      uni.showToast({ title: '投递成功', icon: 'none', duration: TOAST_DURATION })
      return 1
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '投递失败'
      // uni.showToast({ title: errorMessage, icon: 'none', duration: TOAST_DURATION })
      return 0
    }
  }
  /** 群发简历 */
  const sendMassResumeMessage = async (params: SendMassResumeMessageParams) => {
    const { greeting, sendList } = params
    const { data: fileResume } = await resumeQueryMyFileResumeList()
    if (!fileResume || !fileResume.length) {
      return Promise.reject(new Error('请先完善简历信息'))
    }
    const [{ fileId: attachmentId }] = fileResume
    const {
      data: { batchNo, data: sendResumeList },
    } = await exchangeResumeRecordBatchSendResume({
      attachmentId,
      greeting,
      sendList: sendList.map(({ to, ...params }) => params),
    })
    if (!Array.isArray(sendResumeList) || !sendResumeList.length) return 0
    const MAX_CONCURRENT = 3
    const TOAST_DURATION = 2000
    const total = sendResumeList.length
    const isMultiple = total > 1
    const failed: string[] = []
    let timer: ReturnType<typeof setInterval> | null = null
    try {
      uni.showLoading({ title: '投递中...', mask: true })
      if (isMultiple) {
        timer = setInterval(() => {
          uni.showLoading({ title: `投递中 ${total - failed.length}/${total}`, mask: true })
        }, 3000)
      }
      const limit = pLimit(MAX_CONCURRENT)
      const tasks = sendResumeList.map(({ exchangeResumeRecordId, hrUserId }) =>
        limit(async () => {
          const { to } = sendList.find((index) => index.hrUserId === hrUserId)
          try {
            await sendCustomMessage({
              to,
              customEvent: '',
              customExts: {
                type: 'resume',
                status: 0,
                exchange_resume_record_id: exchangeResumeRecordId,
              },
            })
            await sendIMTextMessage(to, greeting)
            return { recipient: to, status: 'fulfilled' } as SendResult
          } catch (err) {
            failed.push(to)
            return { recipient: to, status: 'rejected', reason: err } as SendResult
          }
        }),
      )
      const results = await Promise.all(tasks)
      const sent = results.filter((r) => r.status === 'fulfilled').length
      if (timer) clearInterval(timer)
      uni.hideLoading()
      if (sent > 0) {
        await exchangeResumeRecordBatchSendResumeCallback({
          batchNo,
        })
        uni.showToast({ title: '投递成功', icon: 'none', duration: TOAST_DURATION })
      } else {
        uni.showToast({ title: '投递失败', icon: 'none', duration: TOAST_DURATION })
      }
      return sent
    } catch (e) {
      if (timer) clearInterval(timer)
      uni.hideLoading()
      uni.showToast({ title: '投递失败', icon: 'none', duration: TOAST_DURATION })
      return 0
    } finally {
      if (timer) clearInterval(timer)
    }
  }
  return {
    conversationList,
    totalUnreadCount,
    customCardInfo,
    loadConversations,
    listenForNewMessages,
    getLastTypeMessage,
    getIMUserInfo,
    getIMUserInfoSelf,
    getCoversationInfo,
    getIMLoginId,
    sendIMTextMessage,
    sendGreetingMessage,
    sendResumeMessage,
    sendMassResumeMessage,
    sendCustomResumeMessage,
    sendCustomPhoneMessage,
    sendCustomWeChatCodeMessage,
  }
}
