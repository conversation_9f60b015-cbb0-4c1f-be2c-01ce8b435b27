<route lang="json5">
{
  layout: 'default',
  style: {
    navigationBarTitleText: '',
    enablePullDownRefresh: false,
    navigationStyle: 'custom',
  },
}
</route>
<template>
  <view class="h-screen flex flex-col bg-img">
    <CustomNavBar title="岗位标签"></CustomNavBar>

    <view class="flex flex-1 min-h-0">
      <scroll-view scroll-y class="flex-1 min-w-0">
        <view class="p-40rpx">
          <view class="text-32rpx font-bold mb-30rpx">选择岗位标签</view>
          <view class="grid grid-cols-2 gap-20rpx">
            <view
              v-for="(item, index) in markList"
              :key="index"
              class="h-80rpx text-28rpx font-400 center cursor-pointer border border-solid rounded-10rpx"
              :class="
                item.selected
                  ? 'bg-[#D7DFFF] border-[#649AFF] text-[#649AFF]'
                  : 'border-[#9C9C9C] text-[#333333]'
              "
              @click="selectPositionMark(item, index)"
            >
              {{ item.name }}
            </view>
          </view>
        </view>
      </scroll-view>
    </view>
  </view>
</template>

<script setup lang="ts">
import CustomNavBar from '@/components/CustomNavBar/CustomNavBar.vue'
import { useLoginStore } from '@/store'

const loginStore = useLoginStore()
const markList = ref<AnyArray>([])

// 选择岗位标签
const selectPositionMark = (item: any, index: number) => {
  // 重置所有选中状态
  markList.value.forEach((mark) => {
    mark.selected = false
  })

  // 设置当前选中
  item.selected = true

  // 保存到store
  const selectedMark = {
    selectedMark: item.name,
    selectedMarkCode: item.code,
    markList: markList.value,
  }

  // 更新loginStore中的positionObj
  const currentPositionObj = loginStore.positionObj || {}
  loginStore.setpositionData({
    ...currentPositionObj,
    selectedPositionMark: selectedMark,
  })

  uni.navigateBack()
}

onLoad(() => {
  // 从loginStore获取岗位标签列表
  const currentPositionObj = loginStore.positionObj || {}
  if (
    currentPositionObj.fourthLevelPositions &&
    currentPositionObj.fourthLevelPositions.length > 0
  ) {
    markList.value = currentPositionObj.fourthLevelPositions.map((item: any) => ({
      ...item,
      selected: false,
    }))
  }

  // 如果有已选择的标签，设置选中状态
  if (currentPositionObj.selectedPositionMark) {
    const selectedMark = currentPositionObj.selectedPositionMark
    markList.value.forEach((item) => {
      if (item.code === selectedMark.selectedMarkCode) {
        item.selected = true
      }
    })
  }
})
</script>

<style lang="scss" scoped>
.bg-img {
  background: linear-gradient(125deg, #ffdede 0%, #ebeffa 20%, #ffffff 100%);
}
</style>
