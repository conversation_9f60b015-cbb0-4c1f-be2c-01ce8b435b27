<route lang="json5">
{
  layout: 'default',
  style: {
    navigationBarTitleText: '',
    enablePullDownRefresh: false,
    navigationStyle: 'custom',
  },
}
</route>
<template>
  <z-paging ref="pagingRef" layout-only :paging-style="pageStyle">
    <template #top>
      <CustomNavBar></CustomNavBar>
    </template>
    <view class="page-container">
      <!-- 协议头部 -->
      <view class="protocol-header">
        <text class="protocol-title">易直聘沟通行为规范</text>
        <view class="protocol-meta">
          <text class="version">版本：ver202505</text>
          <text class="date">生效日期：2025年05月24日</text>
        </view>
      </view>

      <!-- 目录导航 -->
      <view class="directory">
        <view class="directory-list">
          <view
            class="directory-item"
            v-for="(item, index) in directory"
            :key="index"
            @click="scrollToSection(index)"
          >
            <!-- <text class="directory-icon">•</text> -->
            <text class="directory-text">{{ item }}</text>
          </view>
        </view>
      </view>

      <!-- 协议内容 -->
      <!-- 第一条 -->
      <view class="protocol-section" id="section-0">
        <text class="section-title">一、禁止侮辱谩骂</text>
        <view class="section-content">
          <text class="section-text">
            1.严禁使用人身攻击（如"蠢货""废物"）、地域/学历歧视（如"乡下人""专科没用"）、性羞辱（如"陪睡可入职"）、外貌侮辱（如"丑八怪"）等言论。
          </text>
          <text class="section-text">
            2.禁止缩写、谐音、表情包、图片等变体形式辱骂（如"SB""**"符号替代）。
          </text>
        </view>
      </view>

      <!-- 第二条 -->
      <view class="protocol-section" id="section-1">
        <text class="section-title">二、禁止骚扰威胁</text>
        <view class="section-content">
          <text class="section-text">
            1.禁止威胁他人人身安全（如"我知道你住哪里"）、职业发展（如"不听话就行业封杀"）、薪资胁迫（如"发裸照才给工资"）。
          </text>
          <text class="section-text">
            2.禁止高频发送无意义信息（如连续10条以上重复消息）、恶意刷屏或要求视频裸聊等行为。
          </text>
        </view>
      </view>

      <!-- 第三条 -->
      <view class="protocol-section" id="section-2">
        <text class="section-title">三、禁止违法违规信息</text>
        <view class="section-content">
          <text class="section-text">
            1.严禁发布传销、集资诈骗（如"投资返利"）、伪造证件、代孕、贩卖隐私数据、违禁药品（如迷药）、非法劳务（如"黑工渠道"）等内容。
          </text>
          <text class="section-text">2.禁止诱导用户参与赌博、色情直播、刷单兼职等违法活动。</text>
        </view>
      </view>

      <!-- 第四条 -->
      <view class="protocol-section" id="section-3">
        <text class="section-title">四、禁止泄露隐私</text>
        <view class="section-content">
          <text class="section-text">
            1.不得强制索要或公开他人身份证正反面、详细户籍地址、病史、银行账户、薪资流水、社交账号密码等信息。
          </text>
          <text class="section-text">
            2.禁止诱导求职者提供与工作无关的隐私（如家庭成员职业、感情状况）。
          </text>
        </view>
      </view>

      <!-- 第五条 -->
      <view class="protocol-section" id="section-4">
        <text class="section-title">五、禁止广告引流</text>
        <view class="section-content">
          <text class="section-text">
            1.禁止发布微商、网贷、医美整形、游戏代练、虚拟货币交易等非招聘类广告。
          </text>
          <text class="section-text">
            2.不得以"高薪兼职""内部推荐"等名义诱导用户添加微信/QQ群或关注公众号。
          </text>
          <text class="section-text">3.禁止使用变体广告（如"V我50""+v看福利"）。</text>
        </view>
      </view>

      <!-- 第六条 -->
      <view class="protocol-section" id="section-5">
        <text class="section-title">六、真实身份认证</text>
        <view class="section-content">
          <text class="section-text">
            1.企业账号需上传有效期内的营业执照、在职证明及HR身份信息，严禁使用已注销公司信息。
          </text>
          <text class="section-text">
            2.个人用户头像需为本人真实照片，禁止使用明星/网图/动物头像，昵称不得含"HR""招聘"等误导性词汇。
          </text>
        </view>
      </view>

      <!-- 第七条 -->
      <view class="protocol-section" id="section-6">
        <text class="section-title">七、违规处理机制</text>
        <view class="section-content">
          <text class="section-text">
            1.文字/语音辱骂：首次屏蔽消息并警告，二次违规禁言3天，三次禁言30天。
          </text>
          <text class="section-text">2.发布违法信息：直接永久封号，并上报网信、公安部门。</text>
          <text class="section-text">3.虚假招聘：冻结账号，需重新提交企业资质人工审核。</text>
          <text class="section-text">
            4.用户可通过聊天框"举报"按钮提交证据，48小时内反馈处理结果。
          </text>
        </view>
      </view>

      <!-- 第八条 -->
      <view class="protocol-section" id="section-7">
        <text class="section-title">八、未成年人保护</text>
        <view class="section-content">
          <text class="section-text">
            企业发布岗位需明示"仅招16周岁以上"，聊天中不得询问未成年人学校地址、父母联系方式，禁止诱导未成年人逃课、离家。
          </text>
        </view>
      </view>

      <!-- 第九条 -->
      <view class="protocol-section" id="section-8">
        <text class="section-title">九、招聘信息规范</text>
        <view class="section-content">
          <text class="section-text">
            1.岗位描述需明确薪资范围（如"5000-8000元"）、工作地点（精确到楼层）、合同类型（全职/兼职/实习），禁止使用"薪资面议""待遇优厚"等模糊表述。
          </text>
          <text class="section-text">
            2.不得虚构福利（如"免费出国旅游"未兑现）、隐藏强制条款（如"入职交押金"）。
          </text>
        </view>
      </view>

      <!-- 第十条 -->
      <view class="protocol-section" id="section-9">
        <text class="section-title">十、文明沟通要求</text>
        <view class="section-content">
          <text class="section-text">
            1.企业需在24小时内回复求职者咨询，禁用"急招！！！"等夸张表述。
          </text>
          <text class="section-text">
            2.建议使用标准化话术（如"请发送简历至邮箱"），避免长段语音（单条不超过30秒）或全英文/方言沟通。
          </text>
          <text class="section-text">
            3.禁止发送问号、感叹号刷屏（如"？？？"、"快点！！！"）等压迫性符号。
          </text>
        </view>
      </view>

      <!-- 第十一条 -->
      <view class="protocol-section" id="section-10">
        <text class="section-title">十一、争议解决指引</text>
        <view class="section-content">
          <text class="section-text">
            1.薪资/合同纠纷需引导至平台"在线仲裁"通道，禁止私下威胁"法庭见"。
          </text>
          <text class="section-text">2.不得通过聊天功能协商赔偿金额，需使用平台电子合同备案。</text>
        </view>
      </view>

      <!-- 第十二条 -->
      <view class="protocol-section" id="section-11">
        <text class="section-title">十二、数据安全条款</text>
        <view class="section-content">
          <text class="section-text">
            聊天记录加密存储，离职招聘者账号由企业管理员72小时内注销，禁止下载或外传聊天数据。
          </text>
        </view>
      </view>
    </view>
  </z-paging>
</template>

<script setup lang="ts">
import CustomNavBar from '@/components/CustomNavBar/CustomNavBar.vue'

// 添加 z-paging 引用
const pagingRef = ref(null)

const { pageStyle } = usePaging({
  style: {
    background: 'linear-gradient( 125deg, #FFDEDE 0%, #EBEFFA 20%, #FFFFFF 100%)',
  },
})
const directory = ref([
  '一、禁止侮辱谩骂',
  '二、禁止骚扰威胁',
  '三、禁止违法违规信息',
  '四、禁止泄露隐私',
  '五、禁止广告引流',
  '六、真实身份认证',
  '七、违规处理机制',
  '八、未成年人保护',
  '九、招聘信息规范',
  '十、文明沟通要求',
  '十一、争议解决指引',
  '十二、数据安全条款',
])

// 直接定位到指定章节 (适配 z-paging)
const scrollToSection = (index: number) => {
  const sectionId = `section-${index}`

  // 在 z-paging 组件中，需要使用组件内部的查询方法
  const query = uni.createSelectorQuery().in(pagingRef.value)

  // 获取目标元素的位置
  query
    .select(`#${sectionId}`)
    .boundingClientRect((targetRect) => {
      if (targetRect && !Array.isArray(targetRect)) {
        // 获取导航栏高度
        const navQuery = uni.createSelectorQuery()
        navQuery
          .select('.customNavbar')
          .boundingClientRect((navBarRect) => {
            let offsetTop = 150 // 默认偏移量

            if (navBarRect && !Array.isArray(navBarRect)) {
              offsetTop = navBarRect.height + 30 // 导航栏高度 + 额外间距
            }

            // 计算目标滚动位置
            const targetScrollTop = targetRect.top - offsetTop

            // 使用 z-paging 的滚动方法
            if (pagingRef.value) {
              try {
                // 方法1: 尝试使用 z-paging 的 scrollToY 方法
                pagingRef.value.scrollToY(Math.max(0, targetScrollTop), false)
              } catch (error) {
                // 方法2: 尝试使用 uni.pageScrollTo
                uni.pageScrollTo({
                  scrollTop: Math.max(0, targetScrollTop),
                  duration: 0,
                  success: () => {
                    console.log('uni.pageScrollTo 成功')
                  },
                })
              }
            }
          })
          .exec()
      }
    })
    .exec()
}
</script>
<style scoped lang="scss">
.setting {
  padding: 0rpx 40rpx;
  .setting-list {
    padding: 30rpx 20rpx;
  }
}
.agreement-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  padding: 20rpx 40rpx;
}

.header {
  margin-bottom: 30rpx;
  text-align: center;
}

.title {
  display: block;
  margin-bottom: 10rpx;
  font-size: 36rpx;
  font-weight: bold;
}

.version,
.effective-date {
  display: block;
  font-size: 24rpx;
  color: #666;
}

.content-list {
  padding: 20rpx 20rpx 0rpx;
  border-radius: 12rpx;
}

.list-title {
  display: block;
  margin-bottom: 20rpx;
  font-size: 28rpx;
  font-weight: bold;
}

.list-item {
  display: flex;
  margin-bottom: 15rpx;
}

.list-icon {
  margin-right: 10rpx;
}

.list-text {
  font-size: 26rpx;
  color: #333;
}

.agreement-content {
  flex: 1;
  padding: 20rpx;
  margin-bottom: 20rpx;
  border-radius: 12rpx;
}

.article {
  margin-bottom: 40rpx;
}

.article-title {
  display: block;
  margin-bottom: 20rpx;
  font-size: 30rpx;
  font-weight: bold;
}

.clause {
  margin-bottom: 15rpx;
}

.clause-text {
  font-size: 26rpx;
  line-height: 1.6;
  color: #333;
}

.footer {
  display: flex;
  justify-content: space-between;
  padding: 20rpx 0;
}

.agree-btn,
.disagree-btn {
  width: 48%;
  height: 80rpx;
  font-size: 28rpx;
  line-height: 80rpx;
  border-radius: 40rpx;
}

.agree-btn {
  color: #fff;
}

.disagree-btn {
  color: #666;
  border: 1rpx solid #ddd;
}
.page-container {
  display: flex;
  flex-direction: column;
  padding: 20rpx 40rpx;
}

.protocol-header {
  padding: 20rpx;

  text-align: center;
  border-radius: 12rpx;
}

.protocol-title {
  display: block;
  margin-bottom: 10rpx;
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
}

.protocol-meta {
  display: flex;
  gap: 30rpx;
  justify-content: center;
}

.version,
.date {
  font-size: 24rpx;
  color: #666;
}

.directory {
  padding: 20rpx;
  margin-bottom: 20rpx;
  border-radius: 12rpx;
}

.directory-title {
  display: block;
  margin-bottom: 15rpx;
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
}

.directory-item {
  display: flex;
  align-items: flex-start;
  padding: 10rpx;
  margin-bottom: 10rpx;
  cursor: pointer;
  border-radius: 8rpx;
  transition: background-color 0.2s ease;

  .directory-text {
    flex: 1;
    font-size: 26rpx;
    color: #007aff;
  }
}

.directory-icon {
  margin-right: 10rpx;
}

.protocol-content {
  flex: 1;
  //   padding: 20rpx;
  margin-bottom: 20rpx;
  border-radius: 12rpx;
}

.protocol-section {
  margin-bottom: 40rpx;
}

.section-title {
  display: block;
  margin-bottom: 15rpx;
  font-size: 30rpx;
  font-weight: bold;
}

.section-content {
  padding-left: 20rpx;
}

.section-text {
  display: block;
  margin-bottom: 10rpx;
  font-size: 26rpx;
  line-height: 1.8;
  color: #333;
}

.action-buttons {
  position: sticky;
  bottom: 0;
  display: flex;
  justify-content: space-between;
  padding: 20rpx 0;
}

.agree-button,
.disagree-button {
  width: 48%;
  height: 80rpx;
  font-size: 28rpx;
  line-height: 80rpx;
  border-radius: 40rpx;
}

.agree-button {
  color: #fff;
}

.disagree-button {
  color: #666;
  border: 1rpx solid #ddd;
}
</style>
