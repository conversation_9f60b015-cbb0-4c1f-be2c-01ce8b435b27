<template>
  <view class="flex flex-col">
    <view
      class="flex items-center gap-20rpx py-32rpx border-b-1px border-b-solid border-b-[#E3E1E1]"
      v-for="(item, key) in conversationList"
      :key="`news-list-${key}`"
      @click="toChatPage(item.conversationId)"
    >
      <view class="size-90rpx">
        <wd-img width="100%" height="100%" round :src="item?.avatarurl" />
      </view>
      <view class="flex items-center flex-1">
        <view class="flex flex-col gap-4rpx flex-1">
          <view class="flex items-center gap-4rpx">
            <view class="flex-1 line-clamp-1">
              <text class="c-#333333 text-28rpx font-500 mr-6rpx">
                {{ item?.nickname }}
              </text>
              <text class="c-#888888 text-24rpx font-500">
                {{
                  [getUserExt(item.ext).companyShortName, getUserExt(item.ext).hrPosition]
                    .filter(Boolean)
                    .join(' | ')
                }}
              </text>
            </view>
          </view>
          <text class="line-clamp-1 c-#888888 text-22rpx">
            {{ getLastTypeMessage(item.lastMessage as any) }}
          </text>
        </view>
        <view class="flex flex-col items-end gap-10rpx">
          <text class="c-#BABABA text-22rpx">
            {{
              formatSmartTime(
                item.lastMessage && 'time' in item.lastMessage ? item.lastMessage.time : '',
              )
            }}
          </text>
          <view class="bg-#FF3333 size-32rpx center border-rd-50%" v-if="!!item.unReadCount">
            <text class="text-20rpx c-#ffffff">
              {{ item.unReadCount > 99 ? '99+' : item.unReadCount }}
            </text>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script lang="ts" setup>
import { formatSmartTime } from '@/utils'

const $emits = defineEmits<{
  (e: 'chat', id: string): void
}>()

const { conversationList, getLastTypeMessage } = useIMConversation()

function getUserExt(ext: string): Api.IM.UserBusinessExtInfo {
  if (ext) {
    return JSON.parse(ext)
  }
  return {
    companyId: 0,
    companyName: '',
    companyShortName: '',
    hrPosition: '',
    hrUserId: 0,
  }
}

const toChatPage = (id: string) => {
  $emits('chat', id)
}
</script>

<style lang="scss" scoped>
//
</style>
