<route lang="json5">
{
  layout: 'default',
  style: {
    navigationBarTitleText: '',
    enablePullDownRefresh: false,
    navigationStyle: 'custom',
  },
}
</route>
<template>
  <z-paging layout-only :paging-style="pageStyle">
    <template #top>
      <CustomNavBar></CustomNavBar>
    </template>
    <view class="box">
      <mp-html :content="rawHtml" />
    </view>
    <template #bottom>
      <view class="btn-fixed">
        <view class="btn_box">
          <view class="btn_bg" @click="generatorSave">生成并保存到简历附件</view>
        </view>
      </view>
    </template>
  </z-paging>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import CustomNavBar from '@/components/CustomNavBar/CustomNavBar.vue'
import { generatorHtml, generatorFileResumeAndSave } from '@/interPost/resume'

const { pageStyle } = usePaging({
  style: {
    background: 'linear-gradient( 125deg, #FFDEDE 0%, #EBEFFA 20%, #FFFFFF 100%)',
  },
})

const rawHtml = ref('')
const processedHtml = ref('')

const generatorHtmlpdf = async () => {
  try {
    const res: any = await generatorHtml({ resumeTemplate: 'resume-template' })

    if (res.code === 0) {
      rawHtml.value = res.data
    }
  } catch (error) {
    console.error('生成HTML失败:', error)
  }
}

const generatorSave = async () => {
  try {
    const res: any = await generatorFileResumeAndSave({ resumeTemplate: 'resume-template' })
    if (res.code === 0) {
      uni.navigateBack()
    } else {
      uni.showToast({ title: res.msg, icon: 'none' })
    }
  } catch (error) {
    console.error('保存简历失败:', error)
    uni.showToast({ title: '保存失败，请重试', icon: 'none' })
  }
}

onMounted(() => {
  generatorHtmlpdf()
})
</script>

<style lang="scss" scoped>
.box {
  box-sizing: border-box;
  min-height: 100vh;
  padding: 20rpx;
}
.btn-fixed {
  box-sizing: border-box;
  justify-content: center;
  padding: 40rpx 40rpx;
  .btn_box {
    box-sizing: border-box;
    width: 100%;
    .btn_bg {
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 20rpx 0rpx;
      font-size: 28rpx;
      font-weight: 500;
      color: #333333;
      background: linear-gradient(90deg, #ffc2c2 0%, #dddcff 100%);
      border-radius: 14px 14px 14px 14px;
    }
  }
}
</style>
